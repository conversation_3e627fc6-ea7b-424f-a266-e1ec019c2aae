import * as Yup from "yup";
import { FormikHelpers } from "formik/dist/types";
import { EMIRATES_ID_REGEX } from "config";

const getInitialValues = {
	ApplyUtilityAllowance: "",
	UtilityProvider: "",
	UtilityAccountNumber: "",
	EIDOfTheUtilityAccountHolder: "",
	RelationToUtilityAccountOwner: null,
	RelationDegree: "",
};

const getValidationSchema = (t) => {
	return Yup.object({
		ApplyUtilityAllowance: Yup.string().required().label("thisField").nullable(),
		UtilityAccountNumber: Yup.number()
			.positive("mustBePositive")
			.when(["ApplyUtilityAllowance"], {
				is: (ApplyUtilityAllowance) => {
					return ApplyUtilityAllowance === "yes";
				},
				then: Yup.number()
					.positive("mustBePositive")
					.typeError("ThisFieldShouldbeNumber")
					.required()
					.label("thisField")
					.nullable(),
				otherwise: Yup.number().notRequired().nullable(),
			}),
		UtilityProvider: Yup.object().when(["ApplyUtilityAllowance"], {
			is: (ApplyUtilityAllowance) => {
				return ApplyUtilityAllowance === "yes";
			},
			then: Yup.object().shape({
				label: Yup.string(),
				value: Yup.string().required().label("thisField"),
			}),
			otherwise: Yup.object().notRequired().nullable(),
		}),
		EIDOfTheUtilityAccountHolder: Yup.string().when(["ApplyUtilityAllowance"], {
			is: (ApplyUtilityAllowance) => {
				return ApplyUtilityAllowance === "yes";
			},
			then: Yup.string()
				.required()
				.label("thisField")
				.matches(EMIRATES_ID_REGEX, "uaeIDNumberError"),
			otherwise: Yup.string().notRequired().nullable(),
		}),
		RelationToUtilityAccountOwner: Yup.object().when(["ApplyUtilityAllowance"], {
			is: (ApplyUtilityAllowance) => {
				return ApplyUtilityAllowance === "yes";
			},
			then: Yup.object()
				.shape({
					label: Yup.string(),
					value: Yup.string().required().label("thisField"),
				})
				.required()
				.typeError("thisField"),
			otherwise: Yup.object().notRequired().nullable(),
		}),
		RelationDegree: Yup.string().when(["ApplyUtilityAllowance", "RelationToUtilityAccountOwner"], {
			is: (ApplyUtilityAllowance, RelationToUtilityAccountOwner) => {
				return ApplyUtilityAllowance === "yes" && RelationToUtilityAccountOwner?.value === "8";
			},
			then: Yup.string().required().label("thisField"),
			otherwise: Yup.string().notRequired().nullable(),
		}),
	});
};

const onChange = (event: any, formikProps: FormikHelpers<any>) => {
	// If the form values change, trigger validation to update button state
	if (
		event &&
		event.target &&
		(event.target.name === "ApplyUtilityAllowance" ||
			event.target.name === "RelationToUtilityAccountOwner")
	) {
		setTimeout(() => formikProps.validateForm(), 0);
	}
};
export { getInitialValues, onChange, getValidationSchema };
