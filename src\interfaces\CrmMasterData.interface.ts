export interface ICrmMasterData<T> {
	Occupations: T[];
	Emirates: T[];
	MaritalStatus: T[];
	Educations: T[];
	accomadations: T[];
	IncomeTypes: T[];
	IncomeSources: T[];
	PensionType: T[];
	PensionAuthority: T[];
	FamilyRelationship: T[];
	RelationWithOwner: T[];
	rentalSource: T[];
	CaseStatus: T[];
	Boolean: T[];
	OtherEntities: T[];
	ProcessTemplates: T[];
	Area: T[];
	Category: T[];
	InflationCategory: T[];
	PortalPersona: T[];
	SubCategory: T[];
	SubPersona: T[];
	Center: T[];
	DocumentProcessTemplate: T[];
	ComplaintTopics: T[];
	ComplaintServices: T[];
	ComplaintSubServices: T[];
	InstallmentsRates: T[];
	ReasonsToEditInflation: T[];
	ReasonsToEditSwp: T[];
	universities: T[];
}
export interface ICrmLookup {
	Id: string;
	Name: string;
	NameAR: string;
	Code: string;
}
export interface ICrmLookupWithParent extends ICrmLookup {
	RelatedId: string;
}
export interface ICrmLookupLocalized {
	value: any;
	label: string;
	RelatedId?: string;
	Code?: string;
}

export const initialCrmMasterData: ICrmMasterData<ICrmLookup> = {
	Occupations: [],
	Emirates: [],
	MaritalStatus: [],
	Educations: [],
	accomadations: [],
	IncomeTypes: [],
	IncomeSources: [],
	PensionType: [],
	PensionAuthority: [],
	FamilyRelationship: [],
	RelationWithOwner: [],
	rentalSource: [],
	CaseStatus: [],
	OtherEntities: [],
	ProcessTemplates: [],
	Category: [],
	InflationCategory: [],
	SubCategory: [],
	Area: [],
	PortalPersona: [],
	SubPersona: [],
	Center: [],
	DocumentProcessTemplate: [],
	ComplaintTopics: [],
	ComplaintServices: [],
	ComplaintSubServices: [],
	InstallmentsRates: [],
	ReasonsToEditInflation: [],
	ReasonsToEditSwp: [],
	universities: [],
	Boolean: [
		{
			Id: "yes",
			Name: "Yes",
			NameAR: "نعم",
			Code: "yes",
		},
		{
			Id: "no",
			Name: "No",
			NameAR: "لا",
			Code: "no",
		},
	],
};
