import { Grid, GridItem } from "@chakra-ui/react";
import FormField from "components/Form/FormField";
import { useFormContext } from "context/FormContext";
import { Form, Formik } from "formik";
import { useTranslation } from "next-i18next";
import { useEffect, useRef, useState } from "react";
import * as functions from "./functions";
import { useRouter } from "next/router";
import { UtilityProviderAr, UtilityProviderEn } from "config";
import { formatEmiratesID } from "utils/formatters";
function RequestDetailsForm({
	onSubmit,
	handleChangeEvent,
	formKey,
	handleSetFormikState,
	initialData,
	readOnly = false,
}) {
	const formikRef = useRef<any>(null);
	const { t } = useTranslation(["forms", "common"]);
	const router = useRouter();
	const { lookups } = useFormContext();

	let [validationSchema] = useState(functions.getValidationSchema(t));

	const updateDropdownValues = () => {
		let originalInitialValues: any = { ...functions.getInitialValues };
		if (initialData) {
			Object.keys(initialData).forEach((key) => {
				if (key in lookups) {
					let indexOfItem = lookups[key].findIndex((val) => val.value === initialData[key]);
					if (indexOfItem >= 0) originalInitialValues[key] = lookups[key][indexOfItem];
				} else {
					originalInitialValues[key] = initialData[key]
						? JSON.parse(JSON.stringify(initialData[key]))
						: initialData[key];
				}
				if (key === "UtilityProvider") {
					let arrayValues = router.locale === "ar" ? UtilityProviderAr : UtilityProviderEn;
					let indexOfItem = arrayValues.findIndex((val) => val.value == initialData[key]);
					if (indexOfItem >= 0) originalInitialValues[key] = arrayValues[indexOfItem];
				}
				// Handle RelationToUtilityAccountOwner field mapping to RelationWithOwner lookup
				if (key === "RelationToUtilityAccountOwner" && lookups.RelationWithOwner) {
					let indexOfItem = lookups.RelationWithOwner.findIndex((val) => val.value == initialData[key]);
					if (indexOfItem >= 0) originalInitialValues[key] = lookups.RelationWithOwner[indexOfItem];
				}
			});
		}

		return originalInitialValues;
	};

	const [initialValues, setInitialValues] = useState(() => updateDropdownValues());

	useEffect(() => {
		setInitialValues(updateDropdownValues());
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [lookups]);

	return (
		<Formik
			enableReinitialize
			initialValues={initialValues}
			validationSchema={validationSchema}
			onSubmit={onSubmit}
			innerRef={formikRef}
			validateOnMount
		>
			{(formik: any) => {
				handleSetFormikState(
					{
						isLoading: formik.isSubmitting,
						isDisabled: !formik.isValid || formik.isSubmitting,
					},
					formKey
				);
				return (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						<Grid
							rowGap={{ base: 6, md: 6 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							{/* Main reason for social aid */}
							<>
								<GridItem colSpan={{ base: 2, md: 1 }}>
									<FormField
										type="radio"
										label={t("ApplyUtilityAllowance")}
										name="ApplyUtilityAllowance"
										value={formik.values["ApplyUtilityAllowance"]}
										touched={formik.touched["ApplyUtilityAllowance"]}
										error={formik.errors["ApplyUtilityAllowance"]}
										options={lookups.Boolean}
										isReadOnly={readOnly}
										onChange={(firstArg) => {
											handleChangeEvent(
												"radio",
												firstArg,
												"ApplyUtilityAllowance",
												formik,
												formKey
											);
											handleChangeEvent("radio", "", "UtilityProvider", formik, formKey);
											handleChangeEvent("radio", "", "UtilityAccountNumber", formik, formKey);
											// Validate form after change to update button state
											setTimeout(() => formik.validateForm(), 0);
										}}
									/>
								</GridItem>
								{formik.values["ApplyUtilityAllowance"] &&
									formik.values["ApplyUtilityAllowance"] === "yes" && (
										<>
											<GridItem colSpan={{ base: 2, md: 1 }}>
												<FormField
													type="selectableTags"
													value={formik.values["UtilityProvider"]}
													options={router.locale === "en" ? UtilityProviderEn : UtilityProviderAr}
													isRequired={true}
													name="UtilityProvider"
													label={t("UtilityProvider")}
													isDisabled={readOnly}
													placeholder={t("placeholder", { ns: "common" })}
													error={formik.errors["UtilityProvider"]}
													touched={formik.touched["UtilityProvider"]}
													onChange={(firstArg) => {
														handleChangeEvent(
															"selectableTags",
															firstArg,
															"UtilityProvider",
															formik,
															formKey
														);
													}}
												/>
											</GridItem>
											<GridItem colSpan={{ base: 2, md: 1 }}>
												<FormField
													type="text"
													isDisabled={readOnly}
													name="UtilityAccountNumber"
													placeholder={t("placeholder", { ns: "common" })}
													label={t("UtilityAccountNumber")}
													value={formik.values["UtilityAccountNumber"]}
													error={formik.errors["UtilityAccountNumber"]}
													onChange={(firstArg) => {
														handleChangeEvent(
															"text",
															firstArg,
															"UtilityAccountNumber",
															formik,
															formKey
														);
													}}
												/>
											</GridItem>
										</>
									)}
							</>

							{/* Utility account holder fields - only show when ApplyUtilityAllowance is "yes" */}
							{formik.values["ApplyUtilityAllowance"] &&
								formik.values["ApplyUtilityAllowance"] === "yes" && (
									<>
										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="text"
												isRequired={true}
												name="EIDOfTheUtilityAccountHolder"
												placeholder={t("placeholder", { ns: "common" })}
												label={t("OwnerEID")}
												value={formik.values["EIDOfTheUtilityAccountHolder"]}
												error={formik.errors["EIDOfTheUtilityAccountHolder"]}
												customFormat={formatEmiratesID}
												isDisabled={readOnly}
												onChange={(firstArg) => {
													handleChangeEvent(
														"text",
														firstArg,
														"EIDOfTheUtilityAccountHolder",
														formik,
														formKey
													);
												}}
											/>
										</GridItem>

										<GridItem colSpan={{ base: 2, md: 1 }}>
											<FormField
												type="selectableTags"
												value={formik.values["RelationToUtilityAccountOwner"]}
												options={lookups.RelationWithOwner}
												isRequired={true}
												name="RelationToUtilityAccountOwner"
												label={t("RelationWithOwner")}
												isDisabled={readOnly}
												placeholder={t("placeholder", { ns: "common" })}
												error={formik.errors["RelationToUtilityAccountOwner"]}
												touched={formik.touched["RelationToUtilityAccountOwner"]}
												onChange={(firstArg) => {
													handleChangeEvent(
														"selectableTags",
														firstArg,
														"RelationToUtilityAccountOwner",
														formik,
														formKey
													);
													// Clear RelationDegree when changing RelationToUtilityAccountOwner
													if (firstArg?.value !== "8") {
														handleChangeEvent("text", "", "RelationDegree", formik, formKey);
													}
													// Validate form after change to update button state
													setTimeout(() => formik.validateForm(), 0);
												}}
											/>
										</GridItem>

										{formik.values["RelationToUtilityAccountOwner"] &&
											formik.values["RelationToUtilityAccountOwner"].value === "8" && (
												<GridItem colSpan={{ base: 2, md: 1 }}>
													<FormField
														type="text"
														isRequired={true}
														name="RelationDegree"
														placeholder={t("placeholder", { ns: "common" })}
														label={t("OtherRelation")}
														value={formik.values["RelationDegree"]}
														error={formik.errors["RelationDegree"]}
														isDisabled={readOnly}
														onChange={(firstArg) => {
															handleChangeEvent(
																"text",
																firstArg,
																"RelationDegree",
																formik,
																formKey
															);
														}}
													/>
												</GridItem>
											)}
									</>
								)}
						</Grid>
					</Form>
				);
			}}
		</Formik>
	);
}

export default RequestDetailsForm;
