import { Box, Button, Flex, Text, useDisclosure } from "@chakra-ui/react";
import MainLayout from "layouts/MainLayout";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";
import ProgressTracker from "components/ProgressTracker";
import { ReactElement, useEffect, useState, useRef } from "react";
import { useRouter } from "next/router";
import AccordionInflation from "pagesComponents/ApplyInflation/AccordionInflation";
import PersonalInformation from "pagesComponents/ApplyInflation/RequestDetails/PersonalInformationForm";
import InformationForm from "pagesComponents/ApplyInflation/RequestDetails/InformationForm";
import FamilyMembersInfoForm from "pagesComponents/ApplyInflation/RequestDetails/FamilyMembersInfoForm";
import ReviewDocument from "pagesComponents/ApplyInflation/RequestDetails/ReviewDocument";
import { useTranslation } from "next-i18next";
import AttachedDocuments from "pagesComponents/ApplyInflation/RequestDetails/AttachedDocuments";
//import NextLink from "next/link";
import useAppToast from "hooks/useAppToast";
import useRouterReady from "hooks/useRouterReady";
import { GetServerSidePropsContext, InferGetServerSidePropsType } from "next";
import { BackendServices } from "services/backend";
import { FormContext } from "context/FormContext";
import { initialCrmMasterData } from "interfaces/CrmMasterData.interface";
import {
	addLocalLookups,
	getContactIdFromToken,
	getLocalizedLookups,
	handleApiErrorMessage,
	mapInflationFormToCaseForm,
	getEmiratesIdFromToken,
	getIsEmiratesIDExpiryDateFromToken,
	getIsEmiratesNationalityFromToken,
	getUserInflationEligibilityDetails,
} from "utils/helpers";

import useFamilyMembers from "pagesComponents/ApplyInflation/RequestDetails/FamilyMembersInfoForm/useFamilyMembers";
import useAttachDocuments from "pagesComponents/ApplyInflation/RequestDetails/AttachedDocuments/useAttachDocuments";

import useInflationRequest from "pagesComponents/ApplyInflation/useInflationRequest";
import { InflationForm } from "interfaces/InflationForm.interface";
import useCustomerPulse from "hooks/useCustomerPulse";
import { modifyRequestFromPending, validateAccount } from "services/frontend";
import {
	BORN_UNKNOWN_PARENTS,
	CUSTOMER_PULSE_AID_LINKING_ID,
	CUSTOMER_PULSE_SCRIPT_LINK,
	STOP_REASON_NOMINATED_CASES,
} from "config";
import Script from "next/script";
import useChildMembers from "pagesComponents/ApplyInflation/RequestDetails/FamilyMembersInfoForm/useChildMembers";
import { useMutation } from "react-query";
import InflationInfoForm from "pagesComponents/ApplyInflation/RequestDetails/InflationInfoForm";
import { ICrmAccountData } from "interfaces/CrmAccountData.interface";

function ApplyInflation({
	masterData,
	userDetails,
	formData,
	isRequestPending,
	requestId,
	customerPulseScriptLink,
	customerPulseLinkingId,
	isMartialEmpty,
	hasSubmitted,
	incomeData,
	guardianEId,
	isEidExp,
	isEmirates,
	applyforAllowance,
	userEligibleAge,
	eligibleInflation,
	IsInflationStandAloneEdit,
	IsInflationBaseEdit,
}: InferGetServerSidePropsType<typeof getServerSideProps>) {
	// Helper function to check if inflation step should be skipped
	const shouldSkipInflationStep = () => {
		return formData?.CaseDetails?.UpdateReason === STOP_REASON_NOMINATED_CASES;
	};

	let tempActiveStep = 0;
	let tempActiveSubStep = 0;
	let tempCurrentStep = 0;
	let hasSSS = false;
	if (formData?.CaseDetails?.CaseRef && formData.CaseDetails.CaseRef.startsWith("SSS")) {
		tempActiveStep = 2;
		tempActiveSubStep = 3;
		tempCurrentStep = 5;
		hasSSS = true;
	}

	if (hasSubmitted) {
		tempActiveStep = 1;
		tempActiveSubStep = 3;
		tempCurrentStep = 4;
	} else if (IsInflationBaseEdit) {
		tempActiveStep = 0;
		tempActiveSubStep = 1;
		tempCurrentStep = 1;
	}
	// if (isRequestPending) {
	// 	tempCurrentStep = 4;
	// 	tempActiveSubStep = 0;
	// 	tempActiveStep = 1;
	// }
	const { t } = useTranslation(["forms", "common", "about", "login"]);
	const router = useRouter();
	const toast = useAppToast();
	const { isOpen, onOpen, onClose } = useDisclosure();
	const routerReady = useRouterReady();
	const { locale, query } = router;
	const [userDetailsState, setUserDetailsState] = useState(() => userDetails);
	const [isSubmitingPending, setIsSubmittingPending] = useState(false);
	const [isFormReadOnly, setIsFormReadOnly] = useState(false);
	const submitButton: any = useRef();
	const username: any = useRef("");
	const reInitializePeronalForm: any = useRef("");
	const caseSavedCategory: any = useRef(formData?.CaseDetails?.InflationCategory);
	const isCategoryChange = useRef(false);
	const [encryptedMobileNumber, setEncryptedMobileNumber] = useState("");
	const [currentStep, setCurrentStep] = useState(tempCurrentStep);
	const [activeStep, setActiveStep] = useState(tempActiveStep);
	const [activeSubIndex, setActiveSubIndex] = useState(tempActiveSubStep);
	// Ref to prevent re-entrant/ cascading auto-proceed calls which can skip multiple steps
	const isAutoProceedingRef = useRef(false);
	const [customerPulseLoading, customerPulseSubmitted, openCustomerPulse] = useCustomerPulse(
		userDetailsState?.EmiratesID!,
		customerPulseLinkingId!
	);

	const { mutateAsync: callValidateAccount, isLoading: isValidatingAccount } = useMutation({
		mutationFn: () =>
			validateAccount(
				caseForm.inflationInformation.UtilityAccountNumber,
				caseForm.inflationInformation.UtilityProvider,
				query.requestId?.toString() ? query.requestId?.toString() : "",
				formData?.ParentCaseId ? formData?.ParentCaseId : "********-0000-0000-0000-********0000",
				IsInflationBaseEdit
					? "InflationBaseEdit"
					: IsInflationNominatedCaseEdit
					? "InflationNominatedCaseEdit"
					: "StandAlone"
			),
		mutationKey: "validateAccount",
	});

	const {
		callGetDocumentList,
		documentList,
		setDocumentStatus,
		getDocumentListLoading,
		attachDocumentsStepDisabled,
		isDocumentUploading,
	} = useAttachDocuments(query.requestId?.toString() || "", activeStep === 1);
	const { familyMembers, setFamilyMembers, familyMembersStepDisabled } = useFamilyMembers(
		formData?.CaseDetails,
		activeStep === 0 && activeSubIndex === 2,
		hasSubmitted
	);

	const { childMembers, setChildMembers } = useChildMembers(formData?.CaseDetails);
	const [khulasitQaidNumber, setKhulasitQaidNumber] = useState("");

	const [caseForm, setCaseForm] = useState((state) =>
		mapInflationFormToCaseForm(state, formData?.CaseDetails, userDetailsState)
	);
	const [stepFormKey, setStepFormKey] = useState("");
	const steps = [
		{
			label: t("enterRequestDetails"),
			subSteps: [
				t("informationForm"),
				t("personalInformation"),
				t("familyMembersInformation"),
				t("inflationInformation"),
			],
		},
		{ label: t("attachedDocuments"), subSteps: [] },
		{ label: t("reviewDetails"), subSteps: [] },
		{ label: t("submitRequest"), subSteps: [] },
	];
	const newBeneficiarySteps = [
		{
			label: t("enterRequestDetails"),
			subSteps: [
				t("informationForm"),
				t("personalInformation"),
				t("familyMembersInformation"),
				t("inflationInformation"),
			],
		},
		{ label: t("attachedDocuments"), subSteps: [] },
		{ label: t("reviewDetails"), subSteps: [] },
		{ label: t("submitRequest"), subSteps: [] },
	];
	useEffect(() => {
		let selectedFormKey = "";
		if (currentStep < sectionsArray.length) {
			selectedFormKey = sectionsArray?.[activeSubIndex]?.formKey || "";
		} else if (
			currentStep >= sectionsArray.length &&
			currentStep < sectionsArray.length + documentsArray.length
		) {
			selectedFormKey = documentsArray?.[0]?.formKey || "";
		} else if (currentStep === maxSteps - 1) {
			selectedFormKey = reviewArray?.[0]?.formKey || "";
		}
		if (selectedFormKey !== stepFormKey) setStepFormKey(() => selectedFormKey);
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [activeStep, activeSubIndex]);

	let isEditChildCase =
		formData?.ParentCaseId && formData?.ParentCaseId !== "********-0000-0000-0000-********0000"
			? true
			: false;
	let IsInflationNominatedCaseEdit = false;
	if (formData?.IsInflationNominatedCaseEdit) {
		IsInflationNominatedCaseEdit = true;
	}
	if (isEditChildCase && (IsInflationNominatedCaseEdit || IsInflationBaseEdit)) {
		userEligibleAge = true;
	}
	useEffect(() => {
		if (isEidExp) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:expEidDate"),
				status: "error",
			});
		} else if (!isEmirates) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:notEmarati"),
				status: "error",
			});
		} else if (!userEligibleAge || !eligibleInflation) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:notEligibleForInflation"),
				status: "error",
			});
		}

		if (hasSubmitted) {
			callGetDocumentList();
		}
	}, []);

	const [buttonState, setFormikState] = useState({
		personalInformation: { isDisabled: true, isLoading: false },
		informationForm: { isDisabled: true, isLoading: false },
		familyMembersInformation: { isDisabled: false, isLoading: false },
		attachedDocuments: { isDisabled: false, isLoading: false },
		reviewDocuments: { isDisabled: false, isLoading: false },
	});
	useEffect(() => {
		if (activeStep === 1 && !isRequestPending && !hasSubmitted) {
			callGetDocumentList();
		}
	}, [activeStep]);
	const handleSetFormikState = (newValues, formKey) => {
		if (
			buttonState?.[formKey]?.isDisabled !== newValues.isDisabled ||
			buttonState?.[formKey]?.isLoading !== newValues.isLoading
		) {
			setFormikState((prev) => ({ ...prev, [formKey]: newValues }));
		}
	};
	const handleAddDeleteFieldArray = (action, name, formKey, newObject, id = null) => {
		if (action === "delete") {
			setCaseForm((prev) => {
				let newState = { ...prev };
				newState[formKey][name].splice(id, 1);
				return newState;
			});
		}
	};

	const handleChangeEvent = (type, firstArg, secondArg, formik, formKey, isFieldArray = false) => {
		if (type === "text" || type === "datetime") {
			handleTextChange(firstArg, secondArg, formik, formKey, isFieldArray, type);
		} else if (type === "selectableTags" || "radio") {
			handleDropdownChange(firstArg, secondArg, formik, formKey, isFieldArray);
		}
	};

	const handelVerifyAccountError = (data: ICrmAccountData) => {
		if (!data?.AccountType) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:Non-ResidentialError"),
				status: "error",
			});
		} else if (!data?.AccountStatus) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:InactiveError"),
				status: "error",
			});
		} else if (!data?.UAENational) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:Non-EmiratiError"),
				status: "error",
			});
		} else if (data?.ReceivingInflationAllowance) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:ReceivingUtilityAidError"),
				status: "error",
			});
		} else if (!data?.IsValidEID) {
			toast({
				title: t("common:genericErrorTitle"),
				description: t("common:utlityAllowanceError"),
				status: "error",
			});
		}
	};
	const handleTextChange = (event, fieldName, formik, formKey, isFieldArray, type) => {
		setCaseForm((prev) => {
			let newState = { ...prev };
			if (isFieldArray) {
				let [parentFieldName, fieldIndex, fieldNameActual] = fieldName.split(".");
				if (newState[formKey][parentFieldName]?.length !== formik.values[parentFieldName])
					newState[formKey][parentFieldName] = formik.values[parentFieldName].map((val) => {
						let newVal = {};
						Object.keys(val).forEach((key) => {
							if (typeof val[key] === "object") {
								newVal[key] = val[key].value || "";
							} else {
								newVal[key] = val[key];
							}
						});
						return newVal;
					});
				if (
					newState[formKey] &&
					newState[formKey][parentFieldName] &&
					newState[formKey][parentFieldName][fieldIndex]
				) {
					if (type === "datetime") {
						newState[formKey][parentFieldName][fieldIndex][fieldNameActual] = event || "";
					} else {
						newState[formKey][parentFieldName][fieldIndex][fieldNameActual] =
							event?.target?.value || "";
					}
				}
				return newState;
			} else {
				if (newState[formKey]) newState[formKey][fieldName] = event?.target?.value || "";
				return newState;
			}
		});
		formik.setFieldValue(fieldName, type === "datetime" ? event || "" : event?.target?.value || "");
	};
	const handleDropdownChange = (value, fieldName, formik, formKey, isFieldArray) => {
		setCaseForm((prev) => {
			let newState = { ...prev };
			if (isFieldArray) {
				let [parentFieldName, fieldIndex, fieldNameActual] = fieldName.split(".");
				if (
					newState[formKey] &&
					newState[formKey][parentFieldName] &&
					newState[formKey][parentFieldName][fieldIndex]
				)
					newState[formKey][parentFieldName][fieldIndex][fieldNameActual] =
						value?.value || value || "";
				return newState;
			} else {
				if (newState[formKey]) newState[formKey][fieldName] = value?.value || value || "";
				return newState;
			}
		});
		formik.setFieldValue(fieldName, value);
	};

	let newBeneficairySections = [
		{
			title: t("informationForm"),
			formKey: "informationForm",
			element: (
				<InformationForm
					innerText={t("socialAidInformationSubtext")}
					key="0"
					handleChangeEvent={handleChangeEvent}
					formKey="informationForm"
					initialData={caseForm.informationForm}
					readOnly={isRequestPending || isFormReadOnly}
					handleSetFormikState={handleSetFormikState}
					userAge={userDetailsState?.Age}
					IsInflationNominatedCaseEdit={IsInflationNominatedCaseEdit}
				/>
			),
		},
		{
			title: t("personalInformation"),
			formKey: "personalInformation",
			element: (
				<PersonalInformation
					innerText={t("personalInformationSubtext")}
					key="1"
					handleChangeEvent={handleChangeEvent}
					formKey={"personalInformation"}
					initialData={caseForm.personalInformation}
					handleSetFormikState={handleSetFormikState}
					isMaritalInit={caseForm.personalInformation.MaritalStatus}
					isMartialEmpty={isMartialEmpty}
					reInitialize={reInitializePeronalForm.current}
					readOnly={isRequestPending || isFormReadOnly || IsInflationBaseEdit}
					isEdit={IsInflationStandAloneEdit || IsInflationBaseEdit}
				/>
			),
		},
		{
			title: t("familyMembersInformation"),
			formKey: "familyMembersInformation",
			element: (
				<FamilyMembersInfoForm
					formKey="familyMembersInformation"
					innerText={t("familyMembersInformationSubtext")}
					key="2"
					familyMembers={familyMembers}
					khulasitQaidNumber={khulasitQaidNumber}
					setFamilyMembers={setFamilyMembers}
					readOnly={isRequestPending || isFormReadOnly || IsInflationBaseEdit}
					childMembers={childMembers}
					setChildMembers={setChildMembers}
					IsInflationBaseEdit={IsInflationBaseEdit}
					IsChildCase={isEditChildCase}
					IsInflationNominatedCaseEdit={IsInflationNominatedCaseEdit}
				/>
			),
		},
		{
			title: t("inflationInformation"),
			formKey: "inflationInformation",
			element: shouldSkipInflationStep() ? (
				<Box p={4} bg="gray.50" borderRadius="md">
					<Text color="gray.600" textAlign="center">
						{t("common:stepSkippedDueToUpdateReason")}
					</Text>
				</Box>
			) : (
				<InflationInfoForm
					key="3"
					handleChangeEvent={handleChangeEvent}
					formKey="inflationInformation"
					initialData={caseForm.inflationInformation}
					readOnly={isRequestPending || isFormReadOnly || shouldSkipInflationStep()}
					handleSetFormikState={handleSetFormikState}
				/>
			),
		},
	];

	let editBeneficairySections = [
		{
			title: t("informationForm"),
			formKey: "informationForm",
			element: (
				<InformationForm
					innerText={t("socialAidInformationSubtext")}
					key="0"
					handleChangeEvent={handleChangeEvent}
					formKey="informationForm"
					initialData={caseForm.informationForm}
					readOnly={true}
					handleSetFormikState={handleSetFormikState}
					userAge={userDetailsState?.Age}
					IsInflationNominatedCaseEdit={IsInflationNominatedCaseEdit}
				/>
			),
		},
		{
			title: t("personalInformation"),
			formKey: "personalInformation",
			element: (
				<PersonalInformation
					innerText={t("personalInformationSubtext")}
					key="1"
					handleChangeEvent={handleChangeEvent}
					formKey="personalInformation"
					isMaritalInit={caseForm.personalInformation.MaritalStatus}
					isMartialEmpty={isMartialEmpty}
					initialData={caseForm.personalInformation}
					handleSetFormikState={handleSetFormikState}
					reInitialize={reInitializePeronalForm.current}
					readOnly={true}
					IsChildCase={isEditChildCase}
					IsInflationBaseEdit={IsInflationBaseEdit}
					IsInflationNominatedCaseEdit={IsInflationNominatedCaseEdit}
				/>
			),
		},
		{
			title: t("familyMembersInformation"),
			formKey: "familyMembersInformation",
			element: (
				<FamilyMembersInfoForm
					formKey="familyMembersInformation"
					innerText={t("familyMembersInformationSubtext")}
					key="2"
					familyMembers={familyMembers}
					khulasitQaidNumber={khulasitQaidNumber}
					setFamilyMembers={setFamilyMembers}
					readOnly={true}
					childMembers={childMembers}
					setChildMembers={setChildMembers}
					IsInflationBaseEdit={IsInflationBaseEdit}
					IsChildCase={isEditChildCase}
				/>
			),
		},
		{
			title: t("inflationInformation"),
			formKey: "inflationInformation",
			element: shouldSkipInflationStep() ? (
				<Box p={4} bg="gray.50" borderRadius="md">
					<Text color="gray.600" textAlign="center">
						{t("common:stepSkippedDueToUpdateReason")}
					</Text>
				</Box>
			) : (
				<InflationInfoForm
					key="3"
					handleChangeEvent={handleChangeEvent}
					formKey="inflationInformation"
					initialData={caseForm.inflationInformation}
					readOnly={isRequestPending || isFormReadOnly || shouldSkipInflationStep()}
					handleSetFormikState={handleSetFormikState}
				/>
			),
		},
	];

	let sectionsArray = hasSubmitted ? editBeneficairySections : newBeneficairySections;
	let documentsArray = [
		{
			title: "",
			formKey: "attachedDocuments",
			element: (
				<AttachedDocuments
					innerText=""
					key="0"
					documentList={documentList}
					setDocumentStatus={setDocumentStatus}
				/>
			),
		},
	];

	let reviewArray = [
		{
			title: t("reviewDetails"),
			formKey: "reviewDetails",
			element: (
				<ReviewDocument
					innerText={t("reviewDetailsSubtext")}
					formKey="reviewDetails"
					setCurrentStep={setCurrentStep}
					handleStepsIndexes={_handleStepsIndexes}
					documentList={documentList}
					familyMembers={familyMembers}
					caseForm={caseForm}
					key="0"
					handleSetFormikState={handleSetFormikState}
					hasSSS={hasSSS}
					childMembers={childMembers}
				/>
			),
		},
	];

	const maxSteps = sectionsArray.length + documentsArray.length + reviewArray.length;
	const { updateRequest, updateRequestLoading } = useInflationRequest(
		caseForm,
		setCaseForm,
		familyMembers,
		setFamilyMembers,
		activeStep,
		activeSubIndex,
		userDetailsState,
		childMembers,
		setChildMembers,
		setKhulasitQaidNumber,
		documentList,
		true,
		isCategoryChange.current
	);

	const isRequestPendingSubmit = activeStep === 1 && isRequestPending;
	const handleProceed = async (type) => {
		if (!isRequestPending) {
			isCategoryChange.current = false;
			setIsFormReadOnly(true);
			if (activeStep === 0 && activeSubIndex === 0) {
				if (caseSavedCategory.current !== caseForm.informationForm.InflationCategory) {
					caseSavedCategory.current = caseForm.informationForm.InflationCategory;
					isCategoryChange.current = true;
				}
			}
			if (activeStep !== 1) {
				// if (type === "submit" && !customerPulseSubmitted) {
				// 	openCustomerPulse();
				// 	return;
				// } else {
				const resp = await updateRequest(type === "submit");
				if (!resp?.IsSuccess) {
					setIsFormReadOnly(false);
					handleApiErrorMessage(resp?.Errors, toast, t, locale);
					return;
				}
				if (activeStep === 0 && activeSubIndex === 1 && !resp?.Data?.CaseDetails?.IsUpdate) {
					setIsFormReadOnly(false);
					//this to indicate the family book is not generated
					return;
				}
				// }
			}
			if (type === "submit") return; // redirect handled by hook
			setIsFormReadOnly(false);
			if ((activeStep === 0 && activeSubIndex === 3) || activeStep === 1) {
				const { data, isError } = await callGetDocumentList();
				if (!data?.IsSuccess === true || isError) {
					toast({
						title: t("common:genericErrorTitle"),
						description: t("common:genericErrorDescription"),
						status: "error",
					});
					return;
				}
			}
		} else {
			if (activeStep === 0 && activeSubIndex === 3) {
				const { data, isError } = await callGetDocumentList();
				if (!data?.IsSuccess === true || isError) {
					toast({
						title: t("common:genericErrorTitle"),
						description: t("common:genericErrorDescription"),
						status: "error",
					});
					return;
				}
			}
			if (activeStep === 1) {
				setIsSubmittingPending(true);
				let docIdList: string[] = [];
				let arr1: any = [];
				let arr2: any = [];
				if (documentList) {
					if (documentList?.ListAdditionalDoc && documentList?.ListAdditionalDoc.length > 0)
						arr1 = documentList?.ListAdditionalDoc?.map((item) => item?.IdDocuments);

					if (documentList?.ListPersonalDocs && documentList?.ListPersonalDocs.length > 0)
						arr2 = documentList?.ListPersonalDocs?.map((item) => item?.IdDocuments);
				}

				docIdList = [...arr1, ...arr2];
				const CaseDetails = {
					ListUploadedDocuments: docIdList,
				};
				const resp = await modifyRequestFromPending(requestId!, CaseDetails);
				if (resp.IsSuccess) {
					router.push(
						`/smart-services/inflation-service/apply-inflation/edited-inflation?requestId=${resp?.Data?.IdCase}`
					);
					return;
				}
				setIsSubmittingPending(false);
				if (!resp?.Data?.IdCase || !resp?.IsSuccess) {
					handleApiErrorMessage(resp?.Errors, toast, t, locale);
					return;
				}
			}
		}
		setCurrentStep((currentIndex) => {
			if (currentIndex >= maxSteps - 1) {
				_handleStepsIndexes(currentIndex);
				return currentIndex;
			}
			_handleStepsIndexes(currentIndex + 1);
			setTimeout(() => {
				window.scrollTo({
					top: 0,
					behavior: "smooth",
				});
			}, 100);
			return currentIndex + 1;
		});
	};
	const handleBack = () => {
		setCurrentStep((currentIndex) => {
			console.log("handleBack called. currentIndex:", currentIndex);
			if (currentIndex <= 0) {
				_handleStepsIndexes(0);
				router.push("/smart-services/inflation-service");
				return 0;
			}
			const target = Math.max(0, currentIndex - 1);
			_handleStepsIndexes(target);
			return target;
		});
	};

	function _handleStepsIndexes(currentIndex) {
		if (currentIndex < sectionsArray.length) {
			setActiveStep(0);
			setActiveSubIndex(currentIndex);
			// Debug logging
			console.log("_handleStepsIndexes called with:", {
				currentIndex,
				IsInflationBaseEdit,
				IsInflationNominatedCaseEdit,
				isEditChildCase,
				shouldSkipInflationStep: shouldSkipInflationStep(),
				updateReason: formData?.CaseDetails?.UpdateReason,
			});
			// Auto-submission logic for edit cases
			if (
				(IsInflationBaseEdit || IsInflationNominatedCaseEdit) &&
				(currentIndex === 1 || currentIndex === 2) &&
				isEditChildCase
			) {
				console.log("Auto-proceeding due to edit case logic for step:", currentIndex);
				// guard against re-entrancy: if already auto-proceeding, skip
				if (!isAutoProceedingRef.current) {
					isAutoProceedingRef.current = true;
					setTimeout(() => {
						handleProceed("proceed");
						// allow future auto-proceed after a short delay
						setTimeout(() => (isAutoProceedingRef.current = false), 300);
					}, 50);
				}
			}

			// Skip inflation information step if UpdateReason indicates it should be skipped
			if (currentIndex === 3 && shouldSkipInflationStep()) {
				console.log("Auto-proceeding due to skip inflation logic for step:", currentIndex);
				if (!isAutoProceedingRef.current) {
					isAutoProceedingRef.current = true;
					setTimeout(() => {
						handleProceed("proceed");
						setTimeout(() => (isAutoProceedingRef.current = false), 300);
					}, 50);
				}
			}
		} else if (
			currentIndex >= sectionsArray.length &&
			currentIndex < sectionsArray.length + documentsArray.length
		) {
			setActiveStep(1);
		} else if (currentIndex === maxSteps - 1) {
			setActiveStep(2);
		}
	}

	const proceedButtonLoading =
		!routerReady ||
		updateRequestLoading ||
		isDocumentUploading !== 0 ||
		(((activeStep === 0 && activeSubIndex === 3) || activeStep === 1) && getDocumentListLoading) || // Refetch document list before documents page and review page
		false;

	const showSaveAsDraft = !!query.requestId && !isRequestPending;

	return (
		<Box w="100%">
			<Script src={customerPulseScriptLink} strategy="afterInteractive" />

			<Box>
				<Flex direction={{ base: "column", md: "row" }}>
					<Box w={{ md: "300px", lg: "400px" }} mr={4} display={{ base: "block", md: "block" }}>
						<ProgressTracker
							activeStep={activeStep}
							activeSubIndex={activeSubIndex}
							steps={hasSubmitted ? steps : newBeneficiarySteps}
							service={t("InflationProgram")}
						/>
					</Box>

					<Box
						pt={{ base: 0, md: 8 }}
						px={{ base: 0, md: 8 }}
						flexGrow={1}
						bg="white"
						boxShadow="unset"
					>
						<FormContext.Provider value={{ lookups: masterData }}>
							<Box mx={0} mb={4}>
								{/* First Section */}
								{currentStep < sectionsArray.length && (
									<AccordionInflation currentStep={currentStep} sectionsArray={sectionsArray} />
								)}
								{/* Second Section */}
								{currentStep >= sectionsArray.length &&
									currentStep < sectionsArray.length + documentsArray.length && (
										<AccordionInflation
											currentStep={0}
											sectionsArray={documentsArray}
											hideBottom={true}
										/>
									)}
								{/* Third Section */}
								{currentStep === maxSteps - 1 && (
									<AccordionInflation currentStep={0} sectionsArray={reviewArray} />
								)}
							</Box>
						</FormContext.Provider>
						{proceedButtonLoading && activeStep === 0 && activeSubIndex === 1 && (
							<Text textAlign={"center"} color={"green"} textDecoration={"under"}>
								{t("generatingFamilyBook")}
							</Text>
						)}
						<Flex mt={{ md: "2rem" }} mb="6.5rem" px={6} justifyContent="end">
							{/* <Box display={{ base: "none", md: "block" }}>
								{showSaveAsDraft && (
									<Box mt={2}>
										{!saveDraftLoading && (
											<Flex
												cursor="pointer"
												color="brand.mainGold"
												alignItems="center"
												onClick={onSaveAsDraft}
											>
												<>
													<SaveIcon mr={2} />
													<Text fontWeight="bold" textDecor="underline">
														{t("saveAsDraft", { ns: "common" })}
													</Text>
												</>
											</Flex>
										)}
										{saveDraftLoading && <Spinner color="brand.mainGold" />}
									</Box>
								)}
							</Box> */}
							<Flex
								w={{ base: "100%", md: "auto" }}
								px={{ base: 0, md: "unset" }}
								pb={{ base: 5, md: "unset" }}
								gap={4}
							>
								<Button
									w={{ base: "50%", md: "13.25rem" }}
									variant="secondary"
									isDisabled={currentStep === 0 || (hasSubmitted && currentStep === 4)}
									onClick={handleBack}
								>
									<Text fontSize={{ base: "xs", md: "md" }} as="span">
										{t("back", { ns: "common" })}
									</Text>
								</Button>
								<Button
									ref={submitButton}
									w={{ base: "50%", md: "13.25rem" }}
									variant="primary"
									onClick={async () => {
										if (
											activeStep === 0 &&
											activeSubIndex === 3 &&
											!shouldSkipInflationStep() &&
											caseForm?.inflationInformation?.ApplyUtilityAllowance === "yes"
										) {
											let resp = await callValidateAccount();
											if (!resp?.IsSuccess) {
												const errors = resp?.Errors;
												if (errors) {
													const parsed = JSON.parse(errors);
													const errMsgEn = parsed.MessageEn;
													if (
														errMsgEn ===
														"An active application has already been submitted for this account"
													) {
														toast({
															title: t("common:info"),
															description: t("common:RecevingUtilityInfo"),
															status: "info",
														});
														handleProceed(currentStep < maxSteps - 1 ? "proceed" : "submit");
														return;
													}
												}
												handleApiErrorMessage(resp?.Errors, toast, t, locale);
												return;
											} else {
												if (
													isEditChildCase &&
													resp.Data?.AccountType &&
													resp.Data?.AccountStatus &&
													resp.Data?.UAENational &&
													resp.Data?.ReceivingInflationAllowance &&
													resp.Data?.IsValidEID
												) {
													if (resp.Data) {
														toast({
															title: t("common:info"),
															description: t("common:RecevingUtilityInfo"),
															status: "info",
														});
													}
													handleProceed(currentStep < maxSteps - 1 ? "proceed" : "submit");
													return;
												} else if (
													!resp.Data?.AccountType ||
													!resp.Data?.AccountStatus ||
													!resp.Data?.UAENational ||
													resp.Data?.ReceivingInflationAllowance ||
													!resp.Data?.IsValidEID
												) {
													if (resp.Data) handelVerifyAccountError(resp.Data);
													return;
												}
												handleProceed(currentStep < maxSteps - 1 ? "proceed" : "submit");
											}
										} else {
											handleProceed(currentStep < maxSteps - 1 ? "proceed" : "submit");
										}
									}}
									isLoading={
										buttonState?.[stepFormKey]?.isLoading ||
										proceedButtonLoading ||
										customerPulseLoading ||
										isSubmitingPending ||
										isValidatingAccount
									}
									disabled={
										buttonState?.[stepFormKey]?.isDisabled ||
										buttonState?.[stepFormKey]?.isLoading ||
										proceedButtonLoading ||
										familyMembersStepDisabled ||
										attachDocumentsStepDisabled ||
										customerPulseLoading ||
										isSubmitingPending ||
										isValidatingAccount ||
										isEidExp ||
										!isEmirates ||
										!userEligibleAge ||
										!eligibleInflation
									}
								>
									{/* <Text as="span">
										{t(
											isRequestPendingSubmit
												? "submit"
												: currentStep < maxSteps - 1 || !customerPulseSubmitted
												? "proceed"
												: "submit",
											{
												ns: "common",
											}
										)}
									</Text> */}
									<Text as="span">
										{t(
											isRequestPendingSubmit
												? "submit"
												: currentStep < maxSteps - 1
												? "proceed"
												: "submit",
											{
												ns: "common",
											}
										)}
									</Text>
								</Button>
							</Flex>
						</Flex>
					</Box>
				</Flex>
			</Box>
		</Box>
	);
}
export async function getServerSideProps(ctx: GetServerSidePropsContext) {
	const c = (await BackendServices.getMasterData())?.Data || initialCrmMasterData;
	const masterData = addLocalLookups(c);
	const emiratesId = await getEmiratesIdFromToken(ctx.req);
	const expDate = await getIsEmiratesIDExpiryDateFromToken(ctx.req);
	const isEmiratesData = await getIsEmiratesNationalityFromToken(ctx.req);
	const isEmirates = isEmiratesData != undefined ? isEmiratesData : true;
	const ToDate = new Date();

	const inflationEligiblityData = await getUserInflationEligibilityDetails(ctx.req);
	let userEligibleAge = true;
	let eligibleInflation = true;
	if (inflationEligiblityData.Age !== undefined) {
		userEligibleAge = inflationEligiblityData?.Age >= 21 ? true : false;
	}

	let isEidExp = false;
	if (expDate) isEidExp = new Date(expDate).getTime() < ToDate.getTime() ? true : false;
	const requestId = ctx.query.requestId?.toString();
	let profile = await BackendServices.retrieveContact(
		emiratesId,
		requestId ? requestId : "********-0000-0000-0000-********0000"
	);
	let userDetails = profile.Data;
	let hasSubmitted = false;
	let isMartialEmpty =
		profile.Data?.MaritalStatus.MaritalStatusId === "********-0000-0000-0000-********0000";

	let formData: InflationForm | null = null;
	let incomeData: any | null = null;
	let isRequestPending = !!ctx.query.isPending?.toString();
	let guardianEId = "";
	let applyforAllowance = false;
	let IsInflationStandAloneEdit = false;
	let IsInflationBaseEdit = false;
	// let eligibleHousing = true;
	// let eligibleEducation = true;
	// let requiredDocumentsList: ICrmDocumentList | null = null;
	const contactId = await getContactIdFromToken(ctx.req);
	let inflationDataCheck = await BackendServices.checkInflationAllowanceEligibility(contactId);
	if (!!requestId) {
		formData = (await BackendServices.getRequest(requestId, contactId))?.Data || null;
		IsInflationStandAloneEdit = formData?.IsInflationStandAloneEdit === true ? true : false;
		IsInflationBaseEdit = formData?.IsInflationBaseEdit === true ? true : false;
		if (inflationDataCheck.IsSuccess && !inflationDataCheck?.Data?.eligibleInflation) {
			eligibleInflation =
				inflationDataCheck?.Data?.IdCase === requestId ||
				inflationDataCheck?.Data?.IdCase === formData?.ParentCaseId;
		}
		if (IsInflationBaseEdit) userEligibleAge = true;
		applyforAllowance =
			formData?.ParentCaseId && formData?.ParentCaseId !== "********-0000-0000-0000-********0000"
				? true
				: false;

		if (
			formData?.CaseDetails?.GuardianEmiratesID &&
			formData.CaseDetails.SubCategory === BORN_UNKNOWN_PARENTS
		) {
			profile = await BackendServices.retrieveContact(
				formData?.CaseDetails?.GuardianEmiratesID,
				requestId ? requestId : "********-0000-0000-0000-********0000"
			);
			userDetails = profile.Data;
			isMartialEmpty =
				profile.Data?.MaritalStatus.MaritalStatusId === "********-0000-0000-0000-********0000";
			guardianEId = formData?.CaseDetails?.GuardianEmiratesID;
		}
		hasSubmitted = formData?.SubmissionTime ? true : false;
		if ((!formData || !!formData.SubmissionTime) && !isRequestPending) {
			return {
				redirect: {
					destination: `/${ctx.locale || "ar"}/smart-services/inflation-service/apply-inflation`,
					permanent: false,
				},
			};
		}
	} else {
		eligibleInflation = inflationDataCheck?.Data
			? inflationDataCheck?.Data?.eligibleInflation
			: true;
	}
	if (isRequestPending) {
		if (
			formData?.IdStatus !==
			masterData.CaseStatus.find((c) => c.Name === "Additional Information Required")?.Id
		) {
			return {
				redirect: {
					destination: `/${ctx.locale || "ar"}/my-cases`,
					permanent: false,
				},
			};
		}
	}

	return {
		props: {
			...(await serverSideTranslations(ctx.locale || "ar", ["common", "tables", "forms", "login"])),
			masterData: getLocalizedLookups(masterData, ctx.locale || "ar"),
			userDetails,
			formData,
			isRequestPending,
			requestId: requestId || null,
			customerPulseScriptLink: CUSTOMER_PULSE_SCRIPT_LINK,
			customerPulseLinkingId: CUSTOMER_PULSE_AID_LINKING_ID,
			isMartialEmpty,
			hasSubmitted,
			incomeData,
			guardianEId,
			isEidExp,
			isEmirates,
			applyforAllowance,
			userEligibleAge,
			eligibleInflation,
			IsInflationStandAloneEdit,
			IsInflationBaseEdit,
		},
	};
}

ApplyInflation.getLayout = function getLayout(page: ReactElement) {
	return <MainLayout>{page}</MainLayout>;
};
export default ApplyInflation;
